@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@500&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Primary Colors */
    --color-primary: #0F5E9C; /* Institutional trust blue - primary */
    --color-primary-50: #EBF4FD; /* Very light blue (50-level shade) - blue-50 */
    --color-primary-100: #D1E7FA; /* Light blue (100-level shade) - blue-100 */
    --color-primary-200: #A3CFF5; /* Medium light blue (200-level shade) - blue-200 */
    --color-primary-500: #3B82F6; /* Medium blue (500-level shade) - blue-500 */
    --color-primary-600: #2563EB; /* Medium dark blue (600-level shade) - blue-600 */
    --color-primary-700: #1D4ED8; /* Dark blue (700-level shade) - blue-700 */
    --color-primary-800: #0F5E9C; /* Primary institutional blue - blue-800 */
    --color-primary-900: #1E3A8A; /* Very dark blue (900-level shade) - blue-900 */

    /* Secondary Colors */
    --color-secondary: #1D3557; /* Professional depth navy - slate-800 */
    --color-secondary-50: #F8FAFC; /* Very light slate (50-level shade) - slate-50 */
    --color-secondary-100: #F1F5F9; /* Light slate (100-level shade) - slate-100 */
    --color-secondary-200: #E2E8F0; /* Medium light slate (200-level shade) - slate-200 */
    --color-secondary-500: #64748B; /* Medium slate (500-level shade) - slate-500 */
    --color-secondary-600: #475569; /* Medium dark slate (600-level shade) - slate-600 */
    --color-secondary-700: #334155; /* Dark slate (700-level shade) - slate-700 */
    --color-secondary-800: #1D3557; /* Secondary professional navy - slate-800 */
    --color-secondary-900: #0F172A; /* Very dark slate (900-level shade) - slate-900 */

    /* Accent Colors */
    --color-accent: #FFD700; /* Opportunity gold - yellow-400 */
    --color-accent-50: #FEFCE8; /* Very light yellow (50-level shade) - yellow-50 */
    --color-accent-100: #FEF3C7; /* Light yellow (100-level shade) - yellow-100 */
    --color-accent-200: #FDE68A; /* Medium light yellow (200-level shade) - yellow-200 */
    --color-accent-300: #FCD34D; /* Medium yellow (300-level shade) - yellow-300 */
    --color-accent-400: #FFD700; /* Accent opportunity gold - yellow-400 */
    --color-accent-500: #F59E0B; /* Medium dark yellow (500-level shade) - yellow-500 */
    --color-accent-600: #D97706; /* Dark yellow (600-level shade) - yellow-600 */

    /* Background Colors */
    --color-background: #F8FAFE; /* Soft clean canvas - blue-50 */
    --color-surface: #FFFFFF; /* Pure white surface - white */

    /* Text Colors */
    --color-text-primary: #1D3557; /* High contrast primary text - slate-800 */
    --color-text-secondary: #6B7280; /* Subtle secondary text - gray-500 */
    --color-text-muted: #9CA3AF; /* Muted text - gray-400 */

    /* Status Colors */
    --color-success: #2A9D8F; /* Growth and positive outcomes - teal-600 */
    --color-success-50: #F0FDFA; /* Very light teal (50-level shade) - teal-50 */
    --color-success-100: #CCFBF1; /* Light teal (100-level shade) - teal-100 */
    --color-success-500: #14B8A6; /* Medium teal (500-level shade) - teal-500 */
    --color-success-600: #2A9D8F; /* Success growth teal - teal-600 */

    --color-warning: #F59E0B; /* Caution amber - amber-500 */
    --color-warning-50: #FFFBEB; /* Very light amber (50-level shade) - amber-50 */
    --color-warning-100: #FEF3C7; /* Light amber (100-level shade) - amber-100 */
    --color-warning-500: #F59E0B; /* Warning caution amber - amber-500 */

    --color-error: #E63946; /* High-risk red - red-600 */
    --color-error-50: #FEF2F2; /* Very light red (50-level shade) - red-50 */
    --color-error-100: #FEE2E2; /* Light red (100-level shade) - red-100 */
    --color-error-500: #EF4444; /* Medium red (500-level shade) - red-500 */
    --color-error-600: #E63946; /* Error high-risk red - red-600 */

    /* Border Colors */
    --color-border: #E5E7EB; /* Minimal borders - gray-200 */
    --color-border-focus: #0F5E9C; /* Active state borders - primary */

    /* Shadow Colors */
    --shadow-card: 0 2px 8px rgba(15, 94, 156, 0.08); /* Card elevation shadow */
    --shadow-modal: 0 4px 16px rgba(15, 94, 156, 0.12); /* Modal elevation shadow */
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-text-primary font-sans;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    /* Improve scroll behavior on mobile */
    scroll-behavior: smooth; /* Enable smooth scrolling for scrollIntoView */
    overscroll-behavior: contain; /* Prevent scroll chaining */
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', sans-serif;
  }

  .font-mono {
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
  }

  /* Mobile scroll optimization */
  @media (max-width: 1023px) {
    html {
      scroll-behavior: smooth; /* Enable smooth scrolling on mobile for scrollIntoView */
    }
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-700 focus:ring-2 focus:ring-primary-200 focus:outline-none transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary-700 focus:ring-2 focus:ring-secondary-200 focus:outline-none transition-colors duration-200;
  }

  .card {
    @apply bg-surface rounded-lg shadow-card border border-border;
  }

  .input-field {
    @apply border border-border rounded-md px-3 py-2 focus:border-primary focus:ring-2 focus:ring-primary-100 focus:outline-none transition-all duration-200;
  }

  .progress-indicator {
    @apply bg-primary-100 rounded-full overflow-hidden;
  }

  .progress-bar {
    @apply bg-primary h-full transition-all duration-300 ease-out;
  }

  .navigation-link {
    @apply text-primary hover:text-primary-700 transition-colors duration-150 ease-out;
  }

  .back-button {
    @apply flex items-center gap-2 text-primary hover:text-primary-700 transition-colors duration-150 ease-out;
  }

  .breathing-animation {
    animation: breathe 2s ease-in-out infinite;
  }

  /* Mobile-Optimized Components */
  .mobile-touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center rounded-lg transition-all duration-200 active:scale-95;
  }

  .mobile-nav-button {
    @apply w-14 h-14 flex items-center justify-center rounded-full shadow-lg transition-all duration-200 active:scale-95 touch-manipulation;
  }

  .mobile-input-field {
    @apply input-field text-base py-4 px-4 min-h-[48px] rounded-lg;
  }

  .mobile-card {
    @apply card p-4 mx-1 rounded-xl touch-manipulation;
  }

  .mobile-section-padding {
    @apply px-2 py-4 sm:px-6 sm:py-8;
  }

  .mobile-text-readable {
    @apply text-base leading-relaxed;
  }

  .mobile-touch-feedback {
    @apply transition-all duration-150 active:scale-95 active:bg-opacity-80 touch-manipulation;
  }

  .mobile-safe-area {
    @apply pb-safe-area-inset-bottom;
  }

  @keyframes breathe {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  .slide-in {
    animation: slideIn 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .fade-in {
    animation: fadeIn 200ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  /* Mobile Swipe Animations */
  .swipe-enter {
    animation: swipeEnter 300ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
    /* Ensure animations don't affect scroll position */
    will-change: opacity, transform;
    backface-visibility: hidden;
  }

  .swipe-exit {
    animation: swipeExit 300ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
    /* Ensure animations don't affect scroll position */
    will-change: opacity, transform;
    backface-visibility: hidden;
  }

  @keyframes swipeEnter {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes swipeExit {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(-20px);
    }
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .transition-smooth {
    transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-height {
    transition: height 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-lift {
    @apply transition-transform duration-150 ease-out hover:-translate-y-0.5;
  }
}